import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/common_functions/common_dateformat_function.dart';
import 'package:staff_medewerker/common/custom_date_picker/date_picker.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/absence_module/bloc/absence_screen_cubit.dart';
import 'package:staff_medewerker/screens/absence_module/repository/my_balances_overview/balance_overview_model.dart';
import 'package:staff_medewerker/screens/absence_module/ui/my_request_screen/my_request_screen.dart';
import 'package:staff_medewerker/screens/absence_module/ui/request_leave_screen/request_leave_screen.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class AbsenceScreen extends StatefulWidget {
  const AbsenceScreen({Key? key}) : super(key: key);

  @override
  State<AbsenceScreen> createState() => _AbsenceScreenState();
}

class _AbsenceScreenState extends State<AbsenceScreen> {
  final absenceBloc =
      BlocProvider.of<AbsenceCubit>(navigatorKey.currentContext!);
  bool isFirstLoad = true;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    absenceBloc.selectedDate = DateFormatFunctions.formatDate(DateTime.now());
    absenceBloc.selectedDateString.value =
        absenceBloc.dateToString(DateTime.now());

    print("absenceBloc.selectedDate: ${absenceBloc.selectedDate}");
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await absenceBloc.myBalancesOverviewApiCall(
          context: context,
          selectedDate: absenceBloc.selectedDate,
          isFirstTime: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.themeColors.homeContainerColor,
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.absence),
      body: BlocBuilder<AbsenceCubit, bool>(
        builder: (ctx, state) {
          return Container(
            color: context.themeColors.homeContainerColor,
            child: Column(
              children: [
                InkWell(
                  onTap: () async {
                    DateTime initialDate2 = absenceBloc
                        .stringToDate(absenceBloc.selectedDateString.value);
                    print("initialDate: $initialDate2");

                    DateTime? selectedDate2 = await CustomDatePicker(
                      context: context,
                      initialDate: initialDate2,
                      firstDate: DateTime(1950),
                      lastDate: DateTime(2100),
                      initialEntryMode: DatePickerEntryMode.calendarOnly,
                      confirmText:
                          AppLocalizations.of(context)!.oK.toUpperCase(),
                      cancelText:
                          AppLocalizations.of(context)!.closeText.toUpperCase(),
                      builder: (context, child) {
                        return Theme(
                          data: ThemeData.light().copyWith(
                              colorScheme: ColorScheme.dark(
                                  onPrimary:
                                      AppColors.white, // selected text color
                                  onSurface: context.themeColors
                                      .textColor, // default date text color
                                  primary:
                                      AppColors.primaryColor, // circle color
                                  surface: context.themeColors
                                      .drawerColor // background color,
                                  ),
                              dialogBackgroundColor:
                                  context.themeColors.listGridColor1,
                              textButtonTheme: TextButtonThemeData(
                                  style: TextButton.styleFrom(
                                      foregroundColor:
                                          AppColors.primaryColor))),
                          child: child!,
                        );
                      },
                    );

                    if (selectedDate2 != null) {
                      // A date was selected by the user
                      if (selectedDate2 != initialDate2) {
                        ctx
                            .read<AbsenceCubit>()
                            .setDate(selectedValue: selectedDate2);

                        String date =
                            DateFormatFunctions.formatDate(selectedDate2);

                        ctx.read<AbsenceCubit>().myBalancesOverviewApiCall(
                            context: context, selectedDate: date);
                        print("Selected date is: $selectedDate2");
                        print("date: $selectedDate2");
                      } else {
                        // The user selected the same date as the initial date
                        print(
                            "User selected the same date as the initial date.");
                      }
                    } else {
                      // No date was selected by the user
                      print("No date was selected by the user.");
                    }

                    // final selectedDate2 = await showDatePickerDialog(
                    //     context: context,
                    //     initialDate: initialDate2,
                    //     minDate: DateTime(DateTime.now().year - 100, 1, 1),
                    //     maxDate: DateTime(DateTime.now().year + 100, 12, 31),
                    //     // selectedDate: initialDate2,
                    //     currentDateColor: Colors.green,
                    //     barrierDismissible: false);
                  },
                  child: Container(
                    height: AppSize.h40,
                    color: context.themeColors.listGridColor1,
                    padding: EdgeInsets.symmetric(horizontal: AppSize.w12),
                    alignment: Alignment.centerLeft,
                    child: Row(
                      children: [
                        Text(
                          AppLocalizations.of(context)!.balanceText,
                          style: context.textTheme.bodyMedium?.copyWith(
                            color: context.themeColors.textColor,
                            fontSize: AppSize.sp15,
                          ),
                        ),
                        Spacer(),
                        ValueListenableBuilder(
                          valueListenable: absenceBloc.selectedDateString,
                          builder:
                              (BuildContext context, value, Widget? child) {
                            return Text(
                              "${value}",
                              style: context.textTheme.bodyMedium?.copyWith(
                                  color: context.themeColors.textColor,
                                  fontSize: AppSize.sp15),
                            );
                          },
                        ),
                        SpaceH(AppSize.w6),
                        Icon(Ionicons.calendar,
                            color: context.themeColors.textColor,
                            size: AppSize.sp16),
                      ],
                    ),
                  ),
                ),
                ValueListenableBuilder(
                  valueListenable: absenceBloc.isLoading,
                  builder:
                      (BuildContext context, bool isLoading, Widget? child) {
                    if (isLoading) {
                      return Column(
                        children: [
                          ShimmerWidget(
                            margin: EdgeInsets.symmetric(
                                horizontal: AppSize.h10, vertical: AppSize.w14),
                            height: AppSize.h10,
                          ),
                          Divider(
                            color: context.themeColors.greyColor,
                          ),
                          ShimmerWidget(
                            margin: EdgeInsets.symmetric(
                                horizontal: AppSize.h10, vertical: AppSize.w14),
                            height: AppSize.h10,
                          ),
                          Divider(
                            color: context.themeColors.greyColor,
                          ),
                          ShimmerWidget(
                            margin: EdgeInsets.symmetric(
                                horizontal: AppSize.h10, vertical: AppSize.w14),
                            height: AppSize.h10,
                          ),
                          Divider(
                            color: context.themeColors.greyColor,
                          ),
                          ShimmerWidget(
                            margin: EdgeInsets.symmetric(
                                horizontal: AppSize.h10, vertical: AppSize.w14),
                            height: AppSize.h10,
                          ),
                          Divider(
                            color: context.themeColors.greyColor,
                          ),
                          ShimmerWidget(
                            margin: EdgeInsets.symmetric(
                                horizontal: AppSize.h10, vertical: AppSize.w14),
                            height: AppSize.h10,
                          ),
                        ],
                      );
                    } else {
                      return ValueListenableBuilder(
                        valueListenable: absenceBloc.myBalancesOverview,
                        builder: (BuildContext context,
                            MyBalancesOverviewModel value, Widget? child) {
                          return Column(
                            children: [
                              ListDataOfAbsence(
                                  title: AppLocalizations.of(context)!.vakText,
                                  data: value.vak ?? '-'),
                              ListDataOfAbsence(
                                  title: AppLocalizations.of(context)!.atvText,
                                  data: value.atv ?? '-'),
                              // value.tvt == null
                              //     ? Container()
                              //        :
                              ListDataOfAbsence(
                                  title: AppLocalizations.of(context)!.tvtText,
                                  data: value.tvt ?? "-"),
                              // value.sparen == null
                              //     ? Container()
                              //     :
                              ListDataOfAbsence(
                                  title:
                                      AppLocalizations.of(context)!.sparenText,
                                  data: value.sparen ?? "-"),
                              Divider(
                                color: context.themeColors.greyColor,
                              ),
                              ListDataOfAbsence(
                                  title:
                                      AppLocalizations.of(context)!.totalText,
                                  data: value.totaal ?? '-'),
                            ],
                          );
                        },
                      );
                    }
                  },
                ),
                SpaceV(AppSize.h34),

                ListTile(
                  onTap: () {
                    AppNavigation.nextScreen(context, MyRequestScreen());
                  },
                  contentPadding: EdgeInsets.only(left: AppSize.w12),
                  title: Text(
                    AppLocalizations.of(context)!.myRequestText,
                    style: context.textTheme.bodyMedium?.copyWith(
                      color: context.themeColors.textColor,
                      fontSize: AppSize.sp15,
                    ),
                  ),
                  trailing: IconButton(
                    onPressed: () {
                      AppNavigation.nextScreen(context, MyRequestScreen());
                    },
                    icon: Icon(
                      Ionicons.chevron_forward_outline,
                      color: context.themeColors.greyColor,
                      size: AppSize.sp18,
                    ),
                  ),
                ),

                // Padding(
                //   padding: EdgeInsets.symmetric(
                //     horizontal: AppSize.w12,
                //     vertical: AppSize.w12,
                //   ),
                //   child: Row(
                //     children: [
                //       Text(
                //         "Vakantie",
                //         style: context.textTheme.bodyMedium?.copyWith(
                //           color: context.themeColors.textColor,
                //           fontSize: AppSize.sp15,
                //         ),
                //       ),
                //       Spacer(),
                //       // print here total month hours according year and month
                //       Text(
                //         "hourBloc",
                //         style: context.textTheme.bodyMedium?.copyWith(
                //           color: context.themeColors.textColor,
                //           fontSize: AppSize.sp15,
                //         ),
                //       ),
                //     ],
                //   ),
                // ),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
          elevation: 10,
          onPressed: () {
            AppNavigation.nextScreen(context, RequestLeaveScreen());
          },
          backgroundColor: AppColors.primaryColor,
          child: Icon(
            Icons.add,
            color: AppColors.white,
          )),
    );
  }
}

class ListDataOfAbsence extends StatelessWidget {
  final String title;
  final String data;

  ListDataOfAbsence({required this.title, required this.data});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: AppSize.w12,
        vertical: AppSize.w12,
      ),
      child: Row(
        children: [
          Text(
            title,
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.themeColors.textColor,
              fontSize: AppSize.sp15,
            ),
          ),
          Spacer(),
          Text(
            data,
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.themeColors.textColor,
              fontSize: AppSize.sp15,
            ),
          ),
        ],
      ),
    );
  }
}
