import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/schedule_module/bloc/schedule_cubit.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/shift_time_formatter.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart';

import '../../../../utils/colors/app_colors.dart';
import '../../bloc/schedule_date_picker_cubit.dart';
import '../../model/schedule_month_model.dart';
import '../../widget/application_reference_popup.dart';
import '../month_screens/schedule_month_time_detail_screen.dart';

class ScheduleCalenderScreen extends StatefulWidget {
  ScheduleCalenderScreen({Key? key}) : super(key: key);

  @override
  State<ScheduleCalenderScreen> createState() => _ScheduleCalenderScreenState();
}

class _ScheduleCalenderScreenState extends State<ScheduleCalenderScreen> {
  List dateMonth = [];
  Map<String, List<ScheduleMonthResponseModel>> dateToData =
      {}; // Changed to String key for month-based caching
  List<ScheduleMonthResponseModel> selectedData = [];
  bool isSelectedData1 = false;
  bool isLoadingData = false; // Add loading state
  CalendarController _controller = CalendarController();

  // Helper method to generate month key for caching
  String _getMonthKey(DateTime date) {
    return DateFormat('yyyyMM').format(date);
  }

  // Method to clear cache when filters change
  void _clearCache() {
    dateToData.clear();
    log("Cache cleared due to filter changes");
  }

  @override
  void initState() {
    super.initState();
    // Listen for filter changes and clear cache when they occur
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final scheduleBloc = context.read<ScheduleCubit>();
      scheduleBloc.filterChangeNotifier.addListener(() {
        log("Filter changed, clearing calendar cache");
        _clearCache();
        if (mounted) {
          setState(() {});
        }
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  // Helper function to determine the color based on the schedule item type
  Color _getItemColor(element, BuildContext context) {
    final scheduleBloc = context.read<ScheduleCubit>();
    final applicationReferenceList =
        scheduleBloc.applicationReferenceList1.value;

    // Check if it's an Open diensten (Open Service)
    if (element.openService != null) {
      final openDienstenItem = applicationReferenceList.firstWhere(
        (item) => item.title == 'Open diensten',
        orElse: () => Item('Open diensten', false, AppColors.openDienstenColor),
      );
      return openDienstenItem.color;
    }
    // Check if it's a Ruilen (Swap) with specific states
    else if (element.swap != null &&
        (element.swap?.state == 'Aangevraagd' ||
            element.swap?.state == 'Geaccepteerd' ||
            element.swap?.state == 'Uitgezet')) {
      final ruilenItem = applicationReferenceList.firstWhere(
        (item) => item.title == 'Ruilen',
        orElse: () => Item('Ruilen', false, AppColors.ruilenColor),
      );
      return ruilenItem.color;
    }
    // Default to Rooster (Regular schedule)
    else {
      final roosterItem = applicationReferenceList.firstWhere(
        (item) => item.title == 'Rooster',
        orElse: () => Item('Rooster', false, AppColors.roosterColor),
      );
      return roosterItem.color;
    }
  }

  // Helper function to get the shift type for display
  String _getShiftType(element) {
    if (element.openService != null) {
      return 'Open diensten';
    } else if (element.swap != null &&
        (element.swap?.state == 'Aangevraagd' ||
            element.swap?.state == 'Geaccepteerd' ||
            element.swap?.state == 'Uitgezet')) {
      return 'Ruilen';
    } else {
      return 'Rooster';
    }
  }

  // Helper function to build trailing icon for shifts
  Widget _buildShiftTrailingIcon(
      ScheduleMonthResponseModel item, BuildContext context) {
    if (item.swap != null &&
        (item.swap?.state == 'Aangevraagd' ||
            item.swap?.state == 'Geaccepteerd' ||
            item.swap?.state == 'Uitgezet')) {
      return Icon(
        Icons.swap_horiz,
        color: context.themeColors.primaryColor,
        size: AppSize.sp24,
      );
    } else if (item.openService != null) {
      return Icon(
        Icons.open_in_new,
        color: context.themeColors.primaryColor,
        size: AppSize.sp24,
      );
    }
    return SizedBox.shrink();
  }

  DataSource _getCalendarDataSource(BuildContext context) {
    final scheduleBloc = context.read<ScheduleCubit>();
    final List<Appointment> appointments = <Appointment>[];
    Map<String, List<ScheduleMonthResponseModel>> dateToShifts = {};

    // Group shifts by date
    scheduleBloc.scheduleDayList1.forEach((element) {
      if (element.date != null) {
        if (!dateToShifts.containsKey(element.date!)) {
          dateToShifts[element.date!] = [];
        }
        dateToShifts[element.date!]!.add(element);
      }
    });

    // Process each date
    dateToShifts.forEach((date, shifts) {
      print("------->${date}");

      // Group shifts by type to get unique colors for this date
      Map<String, List<ScheduleMonthResponseModel>> shiftsByType = {};
      for (var shift in shifts) {
        String shiftType = _getShiftType(shift);
        if (!shiftsByType.containsKey(shiftType)) {
          shiftsByType[shiftType] = [];
        }
        shiftsByType[shiftType]!.add(shift);
      }

      // Add appointments for each shift type present on this date
      List<String> shiftTypes = shiftsByType.keys.toList();

      for (int i = 0; i < shiftTypes.length; i++) {
        String shiftType = shiftTypes[i];
        List<ScheduleMonthResponseModel> shiftsOfType =
            shiftsByType[shiftType]!;
        Color shiftColor = _getItemColor(shiftsOfType.first, context);

        if (i == 0) {
          // First shift type - show department and time
          appointments.add(Appointment(
            startTime: DateTime.parse(date),
            endTime: DateTime.parse(date),
            subject: shiftsOfType.first.costCenters ?? 's',
            color: shiftColor,
          ));

          appointments.add(Appointment(
            startTime: DateTime.parse(date),
            endTime: DateTime.parse(date),
            subject: ShiftTimeFormatter.formatShiftTimeForCalendar(
                shiftsOfType.first.timeFrom, shiftsOfType.first.timeUntil),
            color: shiftColor,
          ));
        }

        // Add colored badges for additional shifts
        if (i > 0) {
          // For additional shift types, show +1, +2, etc. with their respective colors
          appointments.add(Appointment(
            startTime: DateTime.parse(date),
            endTime: DateTime.parse(date),
            subject: '+$i',
            color: shiftColor,
          ));
        } else if (shiftsOfType.length > 1) {
          // For multiple shifts of the same type (first type), show +1, +2, etc.
          for (int j = 1; j < shiftsOfType.length; j++) {
            appointments.add(Appointment(
              startTime: DateTime.parse(date),
              endTime: DateTime.parse(date),
              subject: '+$j',
              color: shiftColor,
            ));
          }
        }

        // Add additional badges for multiple shifts of non-first types
        if (i > 0 && shiftsOfType.length > 1) {
          for (int j = 1; j < shiftsOfType.length; j++) {
            int badgeNumber = i + j;
            appointments.add(Appointment(
              startTime: DateTime.parse(date),
              endTime: DateTime.parse(date),
              subject: '+$badgeNumber',
              color: shiftColor,
            ));
          }
        }
      }
    });

    return DataSource(appointments);
  }

  void _handleDateTap(DateTime selectedDate, ScheduleCubit scheduleBloc,
      void Function(ScheduleMonthResponseModel) showDetailedItem) {
    print('_handleDateTap called for: $selectedDate');

    setState(() {
      scheduleBloc.selectDate = selectedDate;
    });

    DateFormat formatter = DateFormat('yyyy-MM-dd');
    String formattedSelectedDay = formatter.format(selectedDate);

    scheduleBloc.isDayInList = scheduleBloc.scheduleDayList1.any((date) {
      String formattedDay = formatter.format(DateTime.parse(date.date ?? ''));
      log('formattedDay ------------->${formattedDay}');
      return formattedDay == formattedSelectedDay;
    });

    scheduleBloc.updateDayList(scheduleBloc.isDayInList);

    if (scheduleBloc.isDayInList) {
      List<ScheduleMonthResponseModel> matchingItems = [];
      log('------------->${selectedData}');

      if (isSelectedData1 == true) {
        for (var scheduleItem in selectedData) {
          String formattedItemDay =
              formatter.format(DateTime.parse(scheduleItem.date ?? ''));
          if (formattedItemDay == formattedSelectedDay) {
            matchingItems.add(scheduleItem);
          }
        }
      } else {
        for (var scheduleItem in scheduleBloc.scheduleDayList1) {
          String formattedItemDay =
              formatter.format(DateTime.parse(scheduleItem.date ?? ''));
          if (formattedItemDay == formattedSelectedDay) {
            matchingItems.add(scheduleItem);
          }
        }
      }

      if (matchingItems.isNotEmpty) {
        if (matchingItems.length > 1) {
          // Always show bottom sheet for multiple shifts
          print('showModalBottomSheet----------->');
          showModalBottomSheet(
            context: context,
            isDismissible: true,
            enableDrag: true,
            isScrollControlled: true,
            builder: (context) {
              return Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.7,
                  minHeight: MediaQuery.of(context).size.height * 0.3,
                ),
                decoration: BoxDecoration(
                  color: context.themeColors.homeContainerColor,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppSize.sp16),
                    topRight: Radius.circular(AppSize.sp16),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Container(
                      margin: EdgeInsets.only(top: AppSize.sp8),
                      width: AppSize.sp40,
                      height: AppSize.sp4,
                      decoration: BoxDecoration(
                        color: context.themeColors.greyColor,
                        borderRadius: BorderRadius.circular(AppSize.sp2),
                      ),
                    ),
                    // Title
                    Padding(
                      padding: EdgeInsets.all(AppSize.sp16),
                      child: Text(
                        'Shifts for ${DateFormat('d MMMM').format(selectedDate)}',
                        style: context.textTheme.headlineMedium?.copyWith(
                          color: context.themeColors.textColor,
                          fontSize: AppSize.sp16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    // Shifts list
                    Flexible(
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: matchingItems.length,
                        itemBuilder: (context, index) {
                          final item = matchingItems[index];
                          final shiftType = _getShiftType(item);
                          final shiftColor = _getItemColor(item, context);
                          return InkWell(
                            onTap: () {
                              Navigator.pop(context); // Close the bottom sheet
                              showDetailedItem(item);
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: AppSize.w10,
                                  vertical: AppSize.h10),
                              decoration: BoxDecoration(
                                color: context.themeColors.homeContainerColor,
                                border: Border(
                                  left: BorderSide(
                                      color: shiftColor, width: AppSize.sp8),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '${ShiftTimeFormatter.formatShiftTimeForList(item.timeFrom, item.timeUntil)}',
                                        style: context.textTheme.bodyMedium
                                            ?.copyWith(
                                          color: context.themeColors.textColor,
                                          fontSize: AppSize.sp14,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      SpaceV(AppSize.h2),
                                      Text(
                                        '${item.department ?? ''} • $shiftType',
                                        style: context.textTheme.bodySmall
                                            ?.copyWith(
                                          color: context.themeColors.textColor
                                              .withValues(alpha: 0.7),
                                          fontSize: AppSize.sp12,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Padding(
                                      padding:
                                          EdgeInsets.only(right: AppSize.sp16),
                                      child: _buildShiftTrailingIcon(
                                          item, context)),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    SizedBox(height: AppSize.sp16),
                  ],
                ),
              );
            },
          );
        } else {
          showDetailedItem(matchingItems[0]);
        }
      }
    } else {
      print('No data for selected date');
    }
  }

  @override
  Widget build(BuildContext context) {
    void showDetailedItem(ScheduleMonthResponseModel item) {
      AppNavigation.nextScreen(
        context,
        ScheduleMonthTimeDetailScreen(scheduleMonthData: item),
      );
    }

    print('aaa------------>${MediaQuery.of(context).size.height * 0.75}');
    return BlocBuilder<ScheduleCubit, ScheduleState>(
      builder: (ctx, state) {
        final scheduleBloc = ctx.read<ScheduleCubit>();
        _controller.displayDate = scheduleBloc.selectDate;
        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 0,
          ),
          child: FittedBox(
            child: Container(
              color: context.themeColors.homeContainerColor,
              //color: Colors.red,
              height: MediaQuery.of(context).size.height * 0.75,

              width: MediaQuery.of(context).size.width,
              child: MediaQuery(
                data: MediaQuery.of(context).removePadding(removeTop: true),
                child: SfCalendar(
                  controller: _controller,
                  view: CalendarView.month,
                  firstDayOfWeek: DateTime.monday,
                  cellBorderColor: AppColors.transparent,
                  headerHeight:
                      7, // Set the header height to 0 to remove top padding

                  viewHeaderStyle: ViewHeaderStyle(
                    backgroundColor: Colors.transparent,
                    dayTextStyle: context.textTheme.bodyMedium?.copyWith(
                      color: context.themeColors.textColor,
                      fontSize: AppSize.sp14,
                    ),
                  ),
                  todayTextStyle: context.textTheme.bodyMedium?.copyWith(
                    color: context.themeColors.textColor,
                    fontSize: AppSize.sp13,
                  ),
                  headerStyle: CalendarHeaderStyle(
                      textStyle: context.textTheme.bodyMedium?.copyWith(
                    color: AppColors.transparent,
                    fontSize: AppSize.sp13,
                  )),
                  todayHighlightColor: AppColors.transparent,
                  selectionDecoration: BoxDecoration(
                    color: AppColors.transparent,
                    shape: BoxShape.rectangle,
                  ),
                  dataSource: _getCalendarDataSource(context),

                  onTap: (CalendarTapDetails calendarTapDetails) {
                    // Handle tap events - this will always trigger
                    if (calendarTapDetails.date != null) {
                      _handleDateTap(calendarTapDetails.date!, scheduleBloc,
                          showDetailedItem);
                    }
                  },

                  onSelectionChanged:
                      (CalendarSelectionDetails calendarSelectionDetails) {
                    // Just update the selected date for visual feedback
                    setState(() {
                      scheduleBloc.selectDate =
                          calendarSelectionDetails.date ?? DateTime.now();
                    });
                  },
                  cellEndPadding: 5,
                  onViewChanged: (viewChangedDetails) {
                    SchedulerBinding.instance
                        .addPostFrameCallback((duration) async {
                      try {
                        // Prevent multiple simultaneous API calls
                        if (isLoadingData) return;

                        scheduleBloc.selectDate =
                            viewChangedDetails.visibleDates.first;
                        ctx
                            .read<ScheduleTimeCubit>()
                            .setDate(selectedValue: scheduleBloc.selectDate);

                        String monthKey = _getMonthKey(scheduleBloc.selectDate);
                        log("Checking data for month: $monthKey");

                        if (!dateToData.containsKey(monthKey)) {
                          log("Month $monthKey not in cache, fetching data.");
                          isSelectedData1 = false;
                          isLoadingData = true;

                          try {
                            await BlocProvider.of<ScheduleCubit>(context)
                                .fetchCalendarData(context);

                            if (scheduleBloc.scheduleDayList.isNotEmpty) {
                              scheduleBloc
                                  .filterApplicationReferenceListForCalendar();
                              List<ScheduleMonthResponseModel> copy =
                                  List.of(scheduleBloc.scheduleDayList);
                              dateToData[monthKey] = copy;
                              log("Data saved in cache for month: $monthKey");

                              // Use the already filtered data from cubit instead of applying filters again
                              // The cubit's filterApplicationReferenceListForCalendar() already set scheduleDayList1
                              log("Using cubit-filtered data, scheduleDayList1 length: ${scheduleBloc.scheduleDayList1.length}");
                            } else {
                              log("No data received for month: $monthKey");
                              scheduleBloc.scheduleDayList1 = [];
                            }
                          } catch (e) {
                            log("Error fetching calendar data: $e");
                            scheduleBloc.scheduleDayList1 = [];
                          } finally {
                            isLoadingData = false;
                          }
                        } else {
                          log("Using cached data for month: $monthKey");
                          isSelectedData1 = true;
                          selectedData = dateToData[monthKey]!;

                          if (selectedData.isNotEmpty) {
                            // Apply filters to cached data using cubit's filtering
                            scheduleBloc.scheduleDayList = selectedData;
                            scheduleBloc
                                .filterApplicationReferenceListForCalendar();
                            log("Applied cubit filters to cached data for month: $monthKey");
                          } else {
                            log("Cached data is empty for month: $monthKey");
                            scheduleBloc.scheduleDayList1 = [];
                          }
                        }

                        // Ensure UI updates
                        if (mounted) {
                          setState(() {});
                        }
                      } catch (e) {
                        log("Error in onViewChanged: $e");
                        isLoadingData = false;
                        if (mounted) {
                          setState(() {});
                        }
                      }
                    });
                  },

                  appointmentTextStyle: TextStyle(
                      color: navigatorKey
                          .currentContext!.themeColors.homeContainerColor,
                      fontSize: AppSize.sp12,
                      height:
                          1, // Adjust height to reduce vertical space for appointment text

                      fontWeight: FontWeight.w500),

                  monthViewSettings: MonthViewSettings(
                    appointmentDisplayCount: 4,
                    appointmentDisplayMode:
                        MonthAppointmentDisplayMode.appointment,
                    monthCellStyle: MonthCellStyle(
                      todayBackgroundColor:
                          context.themeColors.calendarTodayDateColor,
                      textStyle: context.textTheme.bodyMedium?.copyWith(
                        color: context.themeColors.textColor,
                        fontSize: AppSize.sp14,
                      ),
                    ),
                    showTrailingAndLeadingDates: false,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class DataSource extends CalendarDataSource {
  DataSource(List<Appointment> source) {
    appointments = source;
  }
}
